#!/bin/bash

# Environment setup script for Docker deployment
echo "🔧 AI Blog Platform - Environment Setup"
echo "======================================="

# Check if .env already exists
if [ -f "ai-blog-platform-backend/.env" ]; then
    echo "⚠️  .env file already exists"
    read -p "Do you want to overwrite it? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Keeping existing .env file"
        exit 0
    fi
fi

# Copy template
echo "📋 Creating .env from template..."
if [ -f "ai-blog-platform-backend/.env.production" ]; then
    cp ai-blog-platform-backend/.env.production ai-blog-platform-backend/.env
    echo "✅ .env file created from template"
else
    echo "❌ Template file .env.production not found"
    exit 1
fi

# Interactive setup
echo ""
echo "🔑 Let's configure your environment variables..."
echo "Press Enter to skip optional variables"

# Google Cloud Project
read -p "Enter your Google Cloud Project ID: " gcp_project
if [ ! -z "$gcp_project" ]; then
    sed -i "s/your_google_cloud_project_id/$gcp_project/g" ai-blog-platform-backend/.env
    echo "✅ Google Cloud Project configured"
fi

# MongoDB URI
read -p "Enter MongoDB URI (or press Enter for default): " mongodb_uri
if [ ! -z "$mongodb_uri" ]; then
    sed -i "s|mongodb://localhost:27017/ai-blog-platform-prod|$mongodb_uri|g" ai-blog-platform-backend/.env
    echo "✅ MongoDB URI configured"
fi

# SERP API Key
echo ""
echo "📡 SERP API Configuration (choose one):"
echo "1. SERP API (serpapi.com)"
echo "2. SERPER API (serper.dev)"
echo "3. Perplexity API (perplexity.ai)"
read -p "Which SERP API do you want to configure? (1/2/3): " serp_choice

case $serp_choice in
    1)
        read -p "Enter SERP API Key: " serp_key
        if [ ! -z "$serp_key" ]; then
            sed -i "s/your_serp_api_key/$serp_key/g" ai-blog-platform-backend/.env
            echo "✅ SERP API configured"
        fi
        ;;
    2)
        read -p "Enter SERPER API Key: " serper_key
        if [ ! -z "$serper_key" ]; then
            sed -i "s/your_serper_api_key/$serper_key/g" ai-blog-platform-backend/.env
            echo "✅ SERPER API configured"
        fi
        ;;
    3)
        read -p "Enter Perplexity API Key: " perplexity_key
        if [ ! -z "$perplexity_key" ]; then
            sed -i "s/your_perplexity_api_key/$perplexity_key/g" ai-blog-platform-backend/.env
            echo "✅ Perplexity API configured"
        fi
        ;;
esac

# WordPress Configuration
echo ""
read -p "Do you want to configure WordPress integration? (y/N): " -n 1 -r
echo
if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "Choose WordPress site:"
    echo "1. WattMonk"
    echo "2. Ensite"
    read -p "Which site? (1/2): " wp_choice
    
    case $wp_choice in
        1)
            read -p "Enter WattMonk WordPress username: " wp_user
            read -p "Enter WattMonk WordPress app password: " wp_pass
            if [ ! -z "$wp_user" ] && [ ! -z "$wp_pass" ]; then
                sed -i "s/your_wattmonk_wp_username/$wp_user/g" ai-blog-platform-backend/.env
                sed -i "s/your_wattmonk_wp_app_password/$wp_pass/g" ai-blog-platform-backend/.env
                echo "✅ WattMonk WordPress configured"
            fi
            ;;
        2)
            read -p "Enter Ensite WordPress username: " wp_user
            read -p "Enter Ensite WordPress app password: " wp_pass
            if [ ! -z "$wp_user" ] && [ ! -z "$wp_pass" ]; then
                sed -i "s/your_ensite_wp_username/$wp_user/g" ai-blog-platform-backend/.env
                sed -i "s/your_ensite_wp_app_password/$wp_pass/g" ai-blog-platform-backend/.env
                echo "✅ Ensite WordPress configured"
            fi
            ;;
    esac
fi

# Check for service account key
echo ""
echo "🔑 Checking Google Cloud service account key..."
if [ -f "ai-blog-platform-backend/service_account_key.json" ]; then
    echo "✅ Service account key found"
else
    echo "⚠️  Service account key not found"
    echo "Please download your Google Cloud service account key and save it as:"
    echo "ai-blog-platform-backend/service_account_key.json"
    echo ""
    read -p "Do you want to continue without it? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        echo "Please add the service account key and run this script again"
        exit 1
    fi
fi

echo ""
echo "🎉 Environment setup complete!"
echo ""
echo "📋 Next steps:"
echo "1. Review and edit ai-blog-platform-backend/.env if needed"
echo "2. Add Google Cloud service account key (if not done)"
echo "3. Run validation: ./scripts/docker-validate.sh"
echo "4. Deploy: ./scripts/docker-deploy.sh"
echo ""
echo "📚 For detailed instructions, see DOCKER_SETUP_CHECKLIST.md"
