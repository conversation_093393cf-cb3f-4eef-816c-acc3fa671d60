# Docker Deployment Guide

This guide covers deploying the AI Blog Platform using Docker with production-ready Alpine images.

## Prerequisites

- Docker Engine 20.10+
- Docker Compose 2.0+
- At least 2GB RAM available
- Google Cloud Project with Vertex AI enabled
- Google Cloud service account key
- Environment variables configured

## Google Cloud Setup

1. **Create Google Cloud Project:**
   - Go to [Google Cloud Console](https://console.cloud.google.com/)
   - Create a new project or select existing one
   - Enable Vertex AI API

2. **Create Service Account:**
   ```bash
   # In Google Cloud Console:
   # IAM & Admin > Service Accounts > Create Service Account
   # Grant roles: Vertex AI User, Storage Admin
   # Create and download JSON key
   ```

3. **Place Service Account Key:**
   ```bash
   # Save the downloaded JSON file as:
   # ai-blog-platform-backend/service_account_key.json
   ```

## Quick Start

1. **Clone and navigate to the project:**
   ```bash
   git clone <repository-url>
   cd blog_gen
   ```

2. **Configure environment variables:**
   ```bash
   cp ai-blog-platform-backend/.env.production ai-blog-platform-backend/.env
   # Edit the .env file with your actual values
   ```

3. **Add Google Cloud service account key (for Vertex AI):**
   ```bash
   # Download service account key from Google Cloud Console
   # Place it as: ai-blog-platform-backend/service_account_key.json
   ```

4. **Validate setup:**
   ```bash
   chmod +x scripts/docker-validate.sh
   ./scripts/docker-validate.sh
   ```

5. **Deploy with Docker:**
   ```bash
   chmod +x scripts/docker-deploy.sh
   ./scripts/docker-deploy.sh
   ```

## Manual Deployment

### Build Images

```bash
# Build both services
docker-compose -f docker-compose.prod.yml build

# Build specific service
docker-compose -f docker-compose.prod.yml build backend
docker-compose -f docker-compose.prod.yml build frontend
```

### Start Services

```bash
# Start all services
docker-compose -f docker-compose.prod.yml up -d

# Start with logs
docker-compose -f docker-compose.prod.yml up
```

### Stop Services

```bash
# Stop all services
docker-compose -f docker-compose.prod.yml down

# Stop and remove volumes
docker-compose -f docker-compose.prod.yml down -v
```

## Service Configuration

### Backend Service
- **Port:** 5001
- **Health Check:** `/health`
- **Environment:** Production optimized
- **User:** Non-root (nodeuser)
- **Volumes:** Persistent uploads storage

### Frontend Service
- **Port:** 3001
- **Health Check:** `/health`
- **Environment:** Next.js standalone build
- **User:** Non-root (nextjs)
- **Dependencies:** Waits for backend health

## Environment Variables

### Required Backend Variables
```env
NODE_ENV=production
PORT=5001

# Database
MONGODB_URI=mongodb://localhost:27017/ai-blog-platform

# Vertex AI (Primary AI Service)
GOOGLE_CLOUD_PROJECT=your_google_cloud_project_id
VERTEX_AI_PROJECT=your_google_cloud_project_id
GOOGLE_CLOUD_LOCATION=us-central1
VERTEX_AI_LOCATION=us-central1
VERTEX_AI_MODEL_NAME=gemini-2.0-flash-exp
GOOGLE_APPLICATION_CREDENTIALS=/app/service_account_key.json

# Fallback AI Service
GEMINI_API_KEY=your_gemini_api_key

# SERP APIs (Required for link generation)
SERP_API_KEY=your_serp_api_key
SERPER_API_KEY=your_serper_api_key
PERPLEXITY_API_KEY=your_perplexity_api_key
RAPIDAPI_KEY=your_rapidapi_key

# WordPress Integration
WATTMONK_WORDPRESS_BASE_URL=https://www.wattmonk.com
WATTMONK_WORDPRESS_USERNAME=your_username
WATTMONK_WORDPRESS_APP_PASSWORD=your_app_password

ENSITE_WORDPRESS_BASE_URL=https://ensite-website.com
ENSITE_WORDPRESS_USERNAME=your_username
ENSITE_WORDPRESS_APP_PASSWORD=your_app_password

# AWS S3 (Image Storage)
AWS_ACCESS_KEY_ID=your_aws_access_key
AWS_SECRET_ACCESS_KEY=your_aws_secret_key
AWS_S3_BUCKET_NAME=your_s3_bucket_name
AWS_REGION=ap-south-1
```

### Frontend Variables
```env
NODE_ENV=production
NEXT_PUBLIC_API_URL=http://backend:5001/api
```

## Monitoring

### Health Checks
```bash
# Check backend health
curl http://localhost:5001/health

# Check frontend health
curl http://localhost:3001/health

# Check service status
docker-compose -f docker-compose.prod.yml ps
```

### Logs
```bash
# View all logs
docker-compose -f docker-compose.prod.yml logs -f

# View specific service logs
docker-compose -f docker-compose.prod.yml logs -f backend
docker-compose -f docker-compose.prod.yml logs -f frontend
```

## Troubleshooting

### Common Issues

1. **Build failures:**
   ```bash
   # Clean build
   docker-compose -f docker-compose.prod.yml build --no-cache
   ```

2. **Permission issues:**
   ```bash
   # Check file permissions
   ls -la ai-blog-platform-backend/.env
   chmod 644 ai-blog-platform-backend/.env
   ```

3. **Network connectivity:**
   ```bash
   # Check network
   docker network ls
   docker network inspect blog_gen_blog-platform
   ```

4. **Volume issues:**
   ```bash
   # Check volumes
   docker volume ls
   docker volume inspect blog_gen_backend_uploads
   ```

### Debug Commands

```bash
# Enter backend container
docker-compose -f docker-compose.prod.yml exec backend sh

# Enter frontend container
docker-compose -f docker-compose.prod.yml exec frontend sh

# Check container resources
docker stats

# Inspect service configuration
docker-compose -f docker-compose.prod.yml config
```

## Security Features

- **Alpine Linux:** Minimal attack surface
- **Non-root users:** Services run as unprivileged users
- **Multi-stage builds:** Reduced image size
- **Health checks:** Automatic service monitoring
- **Network isolation:** Services communicate via internal network

## Production Considerations

1. **Reverse Proxy:** Use nginx or traefik for SSL termination
2. **Database:** Use external MongoDB instance
3. **Monitoring:** Implement logging and metrics collection
4. **Backups:** Regular backup of volumes and database
5. **Updates:** Use rolling updates for zero-downtime deployments

## Performance Optimization

- Images use Alpine Linux for minimal size
- Multi-stage builds reduce final image size
- Node.js standalone output for optimal frontend performance
- Persistent volumes for uploads and cache

## Scaling

```bash
# Scale backend service
docker-compose -f docker-compose.prod.yml up -d --scale backend=3

# Use load balancer for multiple instances
# Configure nginx upstream for backend scaling
```
