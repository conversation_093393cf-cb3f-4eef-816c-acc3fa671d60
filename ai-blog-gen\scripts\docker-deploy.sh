#!/bin/bash

# Production Docker deployment script
echo "🚀 Building and deploying AI Blog Platform..."

# Check if .env file exists
if [ ! -f "./ai-blog-platform-backend/.env" ]; then
    echo "❌ Error: .env file not found in ai-blog-platform-backend/"
    echo "Please create the .env file with required environment variables"
    echo "You can copy from .env.production template:"
    echo "cp ai-blog-platform-backend/.env.production ai-blog-platform-backend/.env"
    exit 1
fi

# Check for Google Cloud service account key
if [ ! -f "./ai-blog-platform-backend/service_account_key.json" ]; then
    echo "⚠️  Warning: service_account_key.json not found"
    echo "Vertex AI features may not work without proper authentication"
    echo "Please add your Google Cloud service account key file"
fi

# Validate critical environment variables
echo "🔍 Validating environment configuration..."
if command -v node >/dev/null 2>&1; then
    cd ai-blog-platform-backend
    node check-env.js
    cd ..
else
    echo "⚠️  Node.js not found, skipping environment validation"
fi

# Build images
echo "📦 Building Docker images..."
docker-compose -f docker-compose.prod.yml build --no-cache

if [ $? -ne 0 ]; then
    echo "❌ Build failed!"
    exit 1
fi

# Start services
echo "🔄 Starting services..."
docker-compose -f docker-compose.prod.yml up -d

if [ $? -ne 0 ]; then
    echo "❌ Failed to start services!"
    exit 1
fi

# Wait for services to be healthy
echo "⏳ Waiting for services to be healthy..."
sleep 30

# Check health
echo "🔍 Checking service health..."
docker-compose -f docker-compose.prod.yml ps

# Test endpoints
echo "🧪 Testing endpoints..."
echo "Testing backend health..."
curl -f http://localhost:5001/health || echo "Backend health check failed"

echo "Testing frontend..."
curl -f http://localhost:3001 || echo "Frontend check failed"

echo "✅ Deployment complete!"
echo "Frontend: http://localhost:3001"
echo "Backend: http://localhost:5001"
echo ""
echo "To view logs: docker-compose -f docker-compose.prod.yml logs -f"
echo "To stop: docker-compose -f docker-compose.prod.yml down"
