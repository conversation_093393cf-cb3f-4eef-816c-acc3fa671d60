# syntax=docker/dockerfile:1

# Dependencies stage
FROM node:18-alpine AS deps
WORKDIR /app

# Install only production dependencies
COPY package*.json ./
RUN npm ci --only=production --omit=dev && \
    npm cache clean --force && \
    rm -rf ~/.npm

# Production stage
FROM node:18-alpine AS production

# Install minimal runtime dependencies
RUN apk add --no-cache dumb-init && \
    rm -rf /var/cache/apk/*

# Create app user
RUN addgroup -g 1001 -S nodejs && \
    adduser -S nodeuser -u 1001

WORKDIR /app

# Copy only production dependencies
COPY --from=deps --chown=nodeuser:nodejs /app/node_modules ./node_modules

# Copy application code (excluding dev files)
COPY --chown=nodeuser:nodejs package*.json ./
COPY --chown=nodeuser:nodejs src/ ./src/
COPY --chown=nodeuser:nodejs *.js ./

# Create required directories
RUN mkdir -p uploads logs && \
    chown nodeuser:nodejs uploads logs

# Set permissions for service account key if exists
RUN if [ -f service_account_key.json ]; then \
      chown nodeuser:nodejs service_account_key.json && \
      chmod 600 service_account_key.json; \
    fi

EXPOSE 5001
USER nodeuser

HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD node -e "require('http').get('http://localhost:5001/health', (res) => { process.exit(res.statusCode === 200 ? 0 : 1) })"

ENTRYPOINT ["dumb-init", "--"]
CMD ["npm", "start"]

