"use client"

import { useEffect, useState } from "react"
import { useR<PERSON>er, usePara<PERSON> } from "next/navigation"
import { <PERSON><PERSON><PERSON>, <PERSON>, ExternalLink, Copy, CheckCircle } from "lucide-react"
import { StepperHeader } from "@/components/stepper-header"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { useToast } from "@/hooks/use-toast"
import { api } from "@/lib/api"

export default function DeployingPage() {
  const [currentTask, setCurrentTask] = useState(0)
  const [completedTasks, setCompletedTasks] = useState<number[]>([])
  const [deploymentComplete, setDeploymentComplete] = useState(false)
  const [wordpressUrl, setWordpressUrl] = useState<string | null>(null)
  const [deploymentError, setDeploymentError] = useState<string | null>(null)
  const [draft, setDraft] = useState<any>(null)
  const [seoData, setSeoData] = useState<any>(null)
  const router = useRouter()
  const params = useParams()
  const { toast } = useToast()
  const draftId = params.draftId as string

  const tasks = [
    "Processing content and uploading images",
    "Generating internal and external links",
    "Creating WordPress draft post",
    "Finalizing SEO optimization"
  ]

  // Fetch draft data
  useEffect(() => {
    const fetchDraft = async () => {
      try {
        const draftData = await api.getDraft(draftId)
        console.log('🔍 Fetched draft data:', draftData)
        console.log('🔍 Draft selectedKeyword:', draftData?.selectedKeyword)
        console.log('🔍 Draft title:', draftData?.title)
        console.log('🔍 Draft metaTitle:', draftData?.metaTitle)
        console.log('🔍 Draft metaDescription:', draftData?.metaDescription)
        console.log('🔍 Draft selectedH1:', draftData?.selectedH1)
        console.log('🔍 Draft selectedMetaTitle:', draftData?.selectedMetaTitle)
        console.log('🔍 Draft selectedMetaDescription:', draftData?.selectedMetaDescription)
        setDraft(draftData)
      } catch (error) {
        console.error('Error fetching draft:', error)
      }
    }

    if (draftId) {
      fetchDraft()
    }
  }, [draftId])

  useEffect(() => {
    let interval: NodeJS.Timeout

    const performDeployment = async () => {
      try {
        // Start the visual progress
        interval = setInterval(() => {
          setCurrentTask((prev) => {
            const next = prev + 1
            if (next < tasks.length) {
              setCompletedTasks((completed) => [...completed, prev])
              return next
            } else {
              // Mark last task as completed
              setCompletedTasks((completed) => [...completed, prev])
              clearInterval(interval)
              return prev
            }
          })
        }, 2000) // Each task takes 2 seconds

        // Perform actual deployment
        console.log('🚀 Starting WordPress deployment...')
        const result = await api.deployWordPress(draftId) as any
        console.log('✅ WordPress deployment result:', result)

        // Clear interval and show result
        clearInterval(interval)
        setCompletedTasks([0, 1, 2, 3]) // Mark all tasks complete

        if (result?.success) {
          setDeploymentComplete(true)
          setWordpressUrl(result.editUrl)

          // Debug: Log the result to see what we're getting
          console.log('🔍 WordPress deployment result:', result)
          console.log('🔍 SEO Instructions:', result.seoInstructions)

          // Set SEO data from result or fallback to draft data
          if (result.seoInstructions) {
            setSeoData(result.seoInstructions)
          } else {
            // Fallback: create SEO data from draft and localStorage
            const localKeyword = localStorage.getItem(`keyword_${draftId}`)
            const localMeta = localStorage.getItem(`meta_${draftId}`)
            let localMetaData = null

            try {
              localMetaData = localMeta ? JSON.parse(localMeta) : null
            } catch (e) {
              console.warn('Failed to parse localStorage meta data')
            }

            const fallbackSeoData = {
              focusKeyword: draft?.selectedKeyword || localKeyword || draft?.blogId?.focusKeyword || '',
              metaTitle: draft?.selectedMetaTitle || draft?.metaTitle || localMetaData?.metaTitle || draft?.title || '',
              metaDescription: draft?.selectedMetaDescription || draft?.metaDescription || localMetaData?.metaDescription || draft?.excerpt || ''
            }
            console.log('🔄 Using fallback SEO data:', fallbackSeoData)
            console.log('🔄 Local keyword:', localKeyword)
            console.log('🔄 Local meta:', localMetaData)
            setSeoData(fallbackSeoData)
          }
          toast({
            title: "Deployment successful!",
            description: "Your blog post has been deployed to WordPress.",
          })
        } else {
          throw new Error(result?.message || result?.error || 'Deployment failed')
        }
      } catch (error: any) {
        console.error('❌ WordPress deployment failed:', error)
        clearInterval(interval)
        setDeploymentError(error.message || 'Deployment failed')
        toast({
          title: "Deployment failed",
          description: error.message || "Failed to deploy to WordPress. Please try again.",
          variant: "destructive",
        })
      }
    }

    performDeployment()

    return () => {
      if (interval) clearInterval(interval)
    }
  }, [draftId, toast])

  const handleViewInWordPress = () => {
    if (wordpressUrl) {
      window.open(wordpressUrl, '_blank')
    }
  }

  const handleBackToHome = () => {
    router.push('/')
  }

  const copyToClipboard = async (text: string, label: string) => {
    try {
      await navigator.clipboard.writeText(text)
      toast({
        title: "Copied!",
        description: `${label} copied to clipboard`,
      })
    } catch (error) {
      toast({
        title: "Copy failed",
        description: "Please copy manually",
        variant: "destructive",
      })
    }
  }

  const handleRetryDeployment = () => {
    // Reset state and retry
    setCurrentTask(0)
    setCompletedTasks([])
    setDeploymentComplete(false)
    setDeploymentError(null)
    setWordpressUrl(null)
    // The useEffect will trigger again
    window.location.reload()
  }

  if (deploymentError) {
    return (
      <div className="min-h-screen bg-gray-50">
        <StepperHeader currentStep={4} draftId={draftId} />

        <main className="max-w-5xl mx-auto px-6 py-8">
          <div className="space-y-8">
            {/* Error Header */}
            <div className="text-center space-y-4">
              <div className="w-16 h-16 bg-red-500 rounded-full flex items-center justify-center mx-auto">
                <span className="text-white text-2xl">✕</span>
              </div>
              <h1 className="text-2xl font-bold text-gray-900">Deployment Failed</h1>
              <p className="text-gray-600">{deploymentError}</p>
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                onClick={handleRetryDeployment}
                className="bg-[#0066cc] hover:bg-blue-700"
              >
                Retry Deployment
              </Button>
              <Button
                variant="outline"
                onClick={handleBackToHome}
              >
                Back to Home
              </Button>
            </div>
          </div>
        </main>
      </div>
    )
  }

  if (deploymentComplete) {
    return (
      <div className="min-h-screen bg-gray-50">
        <StepperHeader currentStep={4} draftId={draftId} />
        
        <main className="max-w-5xl mx-auto px-6 py-8">
          <div className="space-y-8">
            {/* Success Header */}
            <div className="text-center space-y-4">
              <div className="w-16 h-16 bg-green-500 rounded-full flex items-center justify-center mx-auto">
                <Check className="h-8 w-8 text-white" />
              </div>
              <h1 className="text-2xl font-bold text-gray-900">Deployment Successful!</h1>
              <p className="text-gray-600">Your blog post has been successfully deployed to WordPress as a draft.</p>
            </div>

            {/* Debug: Show what we have */}
            {process.env.NODE_ENV === 'development' && (
              <div className="max-w-4xl mx-auto mb-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <h4 className="font-medium text-yellow-800 mb-2">Debug Info:</h4>
                <p className="text-sm text-yellow-700">SEO Data: {seoData ? 'Available' : 'Not Available'}</p>
                <p className="text-sm text-yellow-700">Draft: {draft ? 'Available' : 'Not Available'}</p>
                {seoData && <pre className="text-xs mt-2 text-yellow-600">{JSON.stringify(seoData, null, 2)}</pre>}
              </div>
            )}

            {/* SEO Copy-Paste Section */}
            {(seoData || draft) && (
              <Card className="max-w-4xl mx-auto">
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    SEO Information for WordPress (Copy & Paste)
                  </CardTitle>
                  <p className="text-sm text-gray-600">
                    Copy these values and paste them into WordPress RankMath fields to achieve 86/100+ SEO score
                  </p>
                </CardHeader>
                <CardContent className="space-y-4">
                  {/* Focus Keyword */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">Focus Keyword (RankMath)</label>
                    <div className="flex items-center gap-2">
                      <div className="flex-1 p-3 bg-gray-50 border rounded-md font-mono text-sm">
                        {seoData?.focusKeyword || draft?.selectedKeyword || draft?.blogId?.focusKeyword || 'Not specified'}
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(seoData?.focusKeyword || draft?.selectedKeyword || draft?.blogId?.focusKeyword || '', 'Focus Keyword')}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* H1 Title */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">H1 Title (WordPress Post Title)</label>
                    <div className="flex items-center gap-2">
                      <div className="flex-1 p-3 bg-gray-50 border rounded-md text-sm">
                        {draft?.title || draft?.selectedH1 || seoData?.metaTitle || 'Not specified'}
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(draft?.title || draft?.selectedH1 || seoData?.metaTitle || '', 'H1 Title')}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Meta Title */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">Meta Title (RankMath SEO Title)</label>
                    <div className="flex items-center gap-2">
                      <div className="flex-1 p-3 bg-gray-50 border rounded-md text-sm">
                        {seoData?.metaTitle || draft?.metaTitle || draft?.selectedMetaTitle || 'Not specified'}
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(seoData?.metaTitle || draft?.metaTitle || draft?.selectedMetaTitle || '', 'Meta Title')}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Meta Description */}
                  <div className="space-y-2">
                    <label className="text-sm font-medium text-gray-700">Meta Description (RankMath SEO Description)</label>
                    <div className="flex items-center gap-2">
                      <div className="flex-1 p-3 bg-gray-50 border rounded-md text-sm">
                        {seoData?.metaDescription || draft?.metaDescription || draft?.selectedMetaDescription || 'Not specified'}
                      </div>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => copyToClipboard(seoData?.metaDescription || draft?.metaDescription || draft?.selectedMetaDescription || '', 'Meta Description')}
                      >
                        <Copy className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>

                  {/* Instructions */}
                  <div className="mt-6 p-4 bg-blue-50 border border-blue-200 rounded-lg">
                    <h4 className="font-medium text-blue-900 mb-2">How to use in WordPress:</h4>
                    <ol className="text-sm text-blue-800 space-y-1 list-decimal list-inside">
                      <li>Open your WordPress post in the editor</li>
                      <li>Scroll down to the RankMath SEO section</li>
                      <li>Paste the Focus Keyword into the "Focus Keyword" field</li>
                      <li>Paste the Meta Title into the "SEO Title" field</li>
                      <li>Paste the Meta Description into the "Description" field</li>
                      <li>RankMath should show 86/100+ SEO score automatically</li>
                    </ol>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button
                onClick={handleViewInWordPress}
                className="bg-[#0066cc] hover:bg-blue-700"
                disabled={!wordpressUrl}
              >
                <ExternalLink className="h-4 w-4 mr-2" />
                View in WordPress
              </Button>
              <Button
                variant="outline"
                onClick={handleBackToHome}
              >
                Back to Home
              </Button>
            </div>
          </div>
        </main>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50">
      <StepperHeader currentStep={4} draftId={draftId} />
      
      <main className="max-w-5xl mx-auto px-6 py-8">
        <div className="space-y-8">
          {/* Header */}
          <div className="text-center space-y-4">
            <div className="flex items-center justify-center gap-3">
              <Sparkles className="h-8 w-8 text-blue-600 animate-pulse" />
              <h1 className="text-2xl font-bold text-gray-900">Deploying to WordPress</h1>
              <Sparkles className="h-8 w-8 text-blue-600 animate-pulse" />
            </div>
            <p className="text-gray-600">AI is deploying your blog post to WordPress...</p>
          </div>

          {/* Progress Tasks */}
          <div className="max-w-2xl mx-auto space-y-4">
            {tasks.map((task, index) => (
              <div
                key={index}
                className={`flex items-center gap-4 p-4 rounded-lg border transition-all duration-500 ${
                  completedTasks.includes(index)
                    ? "bg-green-50 border-green-200"
                    : currentTask === index
                    ? "bg-blue-50 border-blue-200 shadow-sm"
                    : "bg-white border-gray-200"
                }`}
              >
                <div className="flex-shrink-0">
                  {completedTasks.includes(index) ? (
                    <div className="w-6 h-6 bg-green-500 rounded-full flex items-center justify-center">
                      <Check className="h-4 w-4 text-white" />
                    </div>
                  ) : currentTask === index ? (
                    <div className="w-6 h-6 border-2 border-blue-500 border-t-transparent rounded-full animate-spin" />
                  ) : (
                    <div className="w-6 h-6 bg-gray-200 rounded-full" />
                  )}
                </div>
                <span
                  className={`text-sm font-medium ${
                    completedTasks.includes(index)
                      ? "text-green-700"
                      : currentTask === index
                      ? "text-blue-700"
                      : "text-gray-500"
                  }`}
                >
                  {task}
                </span>
              </div>
            ))}
          </div>


        </div>
      </main>
    </div>
  )
}
