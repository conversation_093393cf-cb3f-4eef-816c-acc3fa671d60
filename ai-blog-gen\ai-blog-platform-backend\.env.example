# WattMonk AI Blog Platform - Environment Configuration
# Copy this file to .env and fill in your actual values

# ===========================================
# DATABASE CONFIGURATION
# ===========================================
MONGODB_URI=mongodb://localhost:27017/ai-blog-platform
# For MongoDB Atlas: mongodb+srv://username:<EMAIL>/ai-blog-platform

# ===========================================
# AI SERVICES CONFIGURATION
# ===========================================
# Vertex AI Configuration (Recommended for Production)
GOOGLE_CLOUD_PROJECT=your_google_cloud_project_id
VERTEX_AI_PROJECT=your_google_cloud_project_id
GOOGLE_CLOUD_LOCATION=us-central1
VERTEX_AI_LOCATION=us-central1
# Service Account Key Path (REQUIRED for Vertex AI)
GOOGLE_APPLICATION_CREDENTIALS=/path/to/your/service-account-key.json
# Vertex AI Model (Latest: gemini-2.0-flash-exp for best performance)
VERTEX_AI_MODEL_NAME=gemini-2.0-flash-exp
# Get from: https://console.cloud.google.com/

# Fallback: Gemini API Key (for development/testing)
GEMINI_API_KEY=your_gemini_api_key_here
# Get from: https://makersuite.google.com/app/apikey

PERPLEXITY_API_KEY=your_perplexity_api_key_here
# Get from: https://www.perplexity.ai/settings/api
# Used as fallback for SERP API when doing competitor research

# ===========================================
# AWS S3 CONFIGURATION (for image storage)
# ===========================================
AWS_ACCESS_KEY_ID=your_aws_access_key_id
AWS_SECRET_ACCESS_KEY=your_aws_secret_access_key
AWS_S3_BUCKET_NAME=your_s3_bucket_name
AWS_REGION=ap-south-1
# Recommended: Create dedicated S3 bucket for blog images

# ===========================================
# WATTMONK WORDPRESS INTEGRATION
# ===========================================
WATTMONK_WORDPRESS_BASE_URL=https://www.wattmonk.com
WATTMONK_WORDPRESS_USERNAME=your_wattmonk_wp_username
WATTMONK_WORDPRESS_APP_PASSWORD=your_wattmonk_wp_app_password

# ===========================================
# ENSITE WORDPRESS INTEGRATION
# ===========================================
ENSITE_WORDPRESS_BASE_URL=https://ensite-website.com
ENSITE_WORDPRESS_USERNAME=your_ensite_wp_username
ENSITE_WORDPRESS_APP_PASSWORD=your_ensite_wp_app_password

# Generate App Password from: WordPress Admin > Users > Profile > Application Passwords

# ===========================================
# COMPETITOR ANALYSIS & SEO APIS
# ===========================================
SERP_API_KEY=your_serp_api_key_here
# Get from: https://serpapi.com/
# If not available, system will automatically fallback to Perplexity API

RAPIDAPI_KEY=your_rapidapi_key_here
# Get from: https://rapidapi.com/

# ===========================================
# TREND ANALYSIS APIS
# ===========================================
GNEWS_API_KEY=your_gnews_api_key_here
# Get from: https://gnews.io/
NEWSDATA_API_KEY=your_newsdata_api_key_here
# Get from: https://newsdata.io/

# ===========================================
# GOOGLE SHEETS INTEGRATION (Optional)
# ===========================================
GOOGLE_SHEETS_API_KEY=your_google_sheets_api_key
BLOG_DATA_SPREADSHEET_ID=your_spreadsheet_id_here
COMPANY_DATA_SPREADSHEET_ID=your_company_spreadsheet_id
# For manual keyword and company data management

# ===========================================
# SERVER CONFIGURATION
# ===========================================
PORT=5001
NODE_ENV=production
FRONTEND_URL=http://localhost:3000
# For production: https://your-frontend-domain.com

# ===========================================
# PRODUCTION DEPLOYMENT
# ===========================================
# For production deployment, ensure all above variables are properly configured
# Test all integrations before going live
# Monitor API usage and rate limits
