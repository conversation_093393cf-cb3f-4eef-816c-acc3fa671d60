# Docker Setup Checklist

## ✅ Pre-Deployment Checklist

### 1. System Requirements
- [ ] Docker Engine 20.10+ installed
- [ ] Docker Compose 2.0+ installed
- [ ] At least 2GB RAM available
- [ ] 5GB disk space available

### 2. Google Cloud Setup
- [ ] Google Cloud Project created
- [ ] Vertex AI API enabled
- [ ] Service Account created with proper roles:
  - [ ] Vertex AI User
  - [ ] Storage Admin (if using Cloud Storage)
- [ ] Service Account key downloaded
- [ ] Key placed at: `ai-blog-platform-backend/service_account_key.json`

### 3. Environment Configuration
- [ ] `.env` file created from template:
  ```bash
  cp ai-blog-platform-backend/.env.production ai-blog-platform-backend/.env
  ```
- [ ] Required variables configured:
  - [ ] `GOOGLE_CLOUD_PROJECT`
  - [ ] `VERTEX_AI_PROJECT`
  - [ ] `VERTEX_AI_LOCATION`
  - [ ] `MONGODB_URI`
  - [ ] At least one SERP API key:
    - [ ] `SERP_API_KEY` OR
    - [ ] `SERPER_API_KEY` OR
    - [ ] `PERPLEXITY_API_KEY`
  - [ ] WordPress credentials (if deploying blogs):
    - [ ] `WATTMONK_WORDPRESS_*` OR
    - [ ] `ENSITE_WORDPRESS_*`

### 4. API Keys Setup
- [ ] **Vertex AI**: Service account key configured
- [ ] **SERP APIs**: At least one configured
  - [ ] SERP API (serpapi.com)
  - [ ] SERPER API (serper.dev)
  - [ ] Perplexity API (perplexity.ai)
- [ ] **WordPress**: App passwords generated
- [ ] **AWS S3**: Keys configured (for image storage)

### 5. File Structure Validation
- [ ] `ai-blog-platform-backend/Dockerfile` exists
- [ ] `ai-blog-platform-frontend/Dockerfile` exists
- [ ] `docker-compose.prod.yml` exists
- [ ] `.dockerignore` files exist
- [ ] `service_account_key.json` exists (if using Vertex AI)

## 🚀 Deployment Steps

### 1. Validate Setup
```bash
chmod +x scripts/docker-validate.sh
./scripts/docker-validate.sh
```

### 2. Deploy Services
```bash
chmod +x scripts/docker-deploy.sh
./scripts/docker-deploy.sh
```

### 3. Verify Deployment
- [ ] Backend health check: `curl http://localhost:5001/health`
- [ ] Frontend health check: `curl http://localhost:3001/health`
- [ ] Services running: `docker-compose -f docker-compose.prod.yml ps`

## 🔧 Post-Deployment Verification

### 1. Test Core Features
- [ ] Access frontend: http://localhost:3001
- [ ] Create a test blog
- [ ] Verify AI content generation
- [ ] Check image generation
- [ ] Test WordPress deployment (if configured)

### 2. Monitor Logs
```bash
# View all logs
docker-compose -f docker-compose.prod.yml logs -f

# View specific service
docker-compose -f docker-compose.prod.yml logs -f backend
```

### 3. Performance Check
- [ ] Memory usage acceptable
- [ ] CPU usage normal
- [ ] Response times good
- [ ] No error logs

## 🚨 Troubleshooting

### Common Issues

1. **Vertex AI Authentication Failed**
   - [ ] Check service account key exists
   - [ ] Verify key file permissions (600)
   - [ ] Confirm project ID matches

2. **SERP API Errors**
   - [ ] Verify at least one SERP API key is valid
   - [ ] Check API quotas and limits
   - [ ] Test API keys manually

3. **WordPress Connection Failed**
   - [ ] Verify WordPress URLs are accessible
   - [ ] Check app passwords are correct
   - [ ] Confirm user has proper permissions

4. **Build Failures**
   - [ ] Check Docker has enough memory
   - [ ] Verify all files exist
   - [ ] Clean build: `docker-compose -f docker-compose.prod.yml build --no-cache`

5. **Container Startup Issues**
   - [ ] Check environment variables
   - [ ] Verify port availability
   - [ ] Review container logs

## 📊 Success Criteria

- [ ] ✅ All containers running healthy
- [ ] ✅ Frontend accessible at http://localhost:3001
- [ ] ✅ Backend API responding at http://localhost:5001
- [ ] ✅ AI content generation working
- [ ] ✅ Image generation functional
- [ ] ✅ WordPress integration operational (if configured)
- [ ] ✅ No critical errors in logs

## 📚 Additional Resources

- [DOCKER_DEPLOYMENT.md](DOCKER_DEPLOYMENT.md) - Detailed deployment guide
- [Google Cloud Vertex AI Setup](https://cloud.google.com/vertex-ai/docs/start/introduction-unified-platform)
- [WordPress App Passwords](https://wordpress.org/support/article/application-passwords/)

---

**Note**: This checklist ensures a complete and successful Docker deployment of the AI Blog Platform with all latest features including Vertex AI integration.
