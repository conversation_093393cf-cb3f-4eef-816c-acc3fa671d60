// services/keywordService.js
const googleSheetsService = require('./googleSheetsService');
const vertexService = require('./geminiService'); // Renamed for clarity (uses Vertex SDK)
const trendService = require('./trendService');
const Draft = require('../models/Draft');
const BlogData = require('../models/BlogData');

class KeywordService {
  async getKeywordsForCompany(companyName, excludeDraftKeywords = true) {
    try {
      console.log(`🎯 Generating company-specific keywords for: ${companyName}`);
      
      // Get company details for better context
      const Company = require('../models/Company');
      let companyContext = null;
      try {
        companyContext = await Company.findOne({ name: companyName });
        console.log(`🏢 Found company context: ${companyContext?.name || 'Not found'}`);
      } catch (error) {
        console.warn('⚠️ Could not fetch company context:', error.message);
      }
  
      const usedKeywords = excludeDraftKeywords ? await this.getUsedKeywords() : [];
      console.log(`🔍 Found ${usedKeywords.length} used keywords to exclude.`);
  
      // 1. Fetch all manual keywords
      let allManualKeywords = [];
      if (process.env.BLOG_DATA_SPREADSHEET_ID) {
        try {
          allManualKeywords = await googleSheetsService.getCompanyManualKeywords(
            process.env.BLOG_DATA_SPREADSHEET_ID,
            companyName
          );
          console.log(`✅ Fetched ${allManualKeywords.length} manual keywords.`);
        } catch (error) {
          console.warn('⚠️ Failed to fetch manual keywords:', error.message);
        }
      }
  
      // 2. Fetch all AI keywords
      let allAiKeywords = [];
      try {
        console.log(`🤖 Generating AI keywords...`);
        allAiKeywords = await this.generateAIKeywordsWithTrends(companyName, 4, companyContext);
        console.log(`✅ Generated ${allAiKeywords.length} AI keywords.`);
      } catch (error) {
        console.warn('⚠️ Failed to generate AI keywords:', error.message);
      }
  
      // 3. Filter both lists
      const unusedManualKeywords = allManualKeywords.filter(
        keyword => !usedKeywords.includes(keyword.focusKeyword.toLowerCase())
      );
      const unusedAiKeywords = allAiKeywords.filter(
        keyword => !usedKeywords.includes(keyword.focusKeyword.toLowerCase())
      );
  
      // 4. Take the first 2 available from each list
      const selectedManualKeywords = unusedManualKeywords.slice(0, 2);
      const selectedAiKeywords = unusedAiKeywords.slice(0, 2);
  
      // 5. Combine them
      let finalKeywords = [...selectedManualKeywords, ...selectedAiKeywords];
  
      // 6. If not enough keywords, generate fresh ones (as per user instruction)
      if (finalKeywords.length < 4) {
        console.log('⚠️ Not enough keywords, the system will generate fresh ones if needed.');
        // The logic to generate more keywords if needed will be handled by the user/frontend
      }
  
      return finalKeywords.slice(0, 4);
  
    } catch (error) {
      console.error('Error getting keywords:', error);
      // Return fallback keywords if everything fails
      return this.getFallbackKeywords(companyName).slice(0, 4);
    }
  }
  
  async generateAIKeywords(companyName) {
    try {
      // First, get trending solar topics from news APIs
      let trendingTopics = '';
      try {
        console.log('🔍 Fetching trending solar topics...');
        const trendData = await trendService.fetchTrendData('solar energy', 'newsdata', 5);
        if (trendData && trendData.length > 0) {
          const topics = trendData.map(item => item.title).join(', ');
          trendingTopics = `\n\nCurrent trending solar topics from news: ${topics}`;
          console.log('✅ Found trending topics for keyword generation');
        }
      } catch (error) {
        console.warn('⚠️ Could not fetch trending topics:', error.message);
      }

      // Customize prompt based on company
      const isEnsite = companyName && companyName.toLowerCase().includes('ensite');

      let companySpecificGuidelines = '';
      let companyExamples = '';

      if (isEnsite) {
        companySpecificGuidelines = `
      - Focus on ENGINEERING and TECHNICAL solar services (Ensite specializes in solar engineering)
      - Emphasize permit design, structural engineering, and technical consulting
      - Include keywords related to solar project engineering and design services
      - Target engineering professionals, solar developers, and technical audiences`;

        companyExamples = `
      Good examples for Ensite: "solar structural load calculations", "solar permit design requirements", "solar engineering standards", "solar project technical specifications"`;
      } else {
        companySpecificGuidelines = `
      - Focus on INSTALLATION and DESIGN solar services
      - Emphasize installation, design, and customer-facing services
      - Include keywords related to solar installation and design services
      - Target homeowners, property owners, and end customers`;

        companyExamples = `
      Good examples: "commercial solar ROI calculator", "solar panel degradation rates", "net metering benefits", "solar inverter troubleshooting"`;
      }

      const prompt = `Generate exactly 4 relevant blog post keywords for a solar company called "${companyName}".

      IMPORTANT GUIDELINES:
      - Focus on COMPANY-SPECIFIC solar industry keywords for SEO and content marketing
      - DO NOT include generic location keywords like "India", "USA", "California", "near me", etc.
      - DO NOT include generic terms like "best solar company" or "top solar installer"
      - Focus on technical, educational, and service-specific topics
      - Make keywords relevant to solar industry expertise and services
      - Avoid overused keywords like "solar PTO", "solar system design guide", "photovoltaic software"
      ${companySpecificGuidelines}
      ${trendingTopics}

      ${companyExamples}
      Bad examples: "best solar company in India", "top solar installers California", "solar companies near me"

      Return only a JSON array with exactly 4 objects containing:
      - focusKeyword: the main keyword phrase (company-specific, no location terms)
      - articleFormat: one of "how-to", "guide", "listicle", "comparison", "review", "analysis"
      - wordCount: suggested word count like "1200-1800" (MAX 2000 words)
      - targetAudience: who this content is for (be specific)
      - objective: what the goal of this content is
      - source: "vertex"

      Example format:
      [
        {"focusKeyword": "solar financing options", "articleFormat": "guide", "wordCount": "1500-2000", "targetAudience": "Homeowners researching solar", "objective": "Lead generation", "source": "vertex"},
        {"focusKeyword": "solar battery storage systems", "articleFormat": "comparison", "wordCount": "1600-2000", "targetAudience": "Property owners", "objective": "Education", "source": "vertex"}
      ]`;

      const response = await vertexService.generateContent(prompt, { name: companyName });

      // Try to parse JSON from the response
      let aiKeywords = [];
      try {
        // Extract JSON from response if it's wrapped in text
        const jsonMatch = response.content.match(/\[[\s\S]*\]/);
        if (jsonMatch) {
          aiKeywords = JSON.parse(jsonMatch[0]);
        } else {
          aiKeywords = JSON.parse(response.content);
        }

        // Filter out any keywords that contain location terms
        const locationTerms = ['india', 'usa', 'california', 'texas', 'florida', 'new york', 'delhi', 'mumbai', 'bangalore', 'near me', 'in my area', 'local', 'best company', 'top company'];
        aiKeywords = aiKeywords.filter(keyword => {
          const keywordLower = keyword.focusKeyword.toLowerCase();
          return !locationTerms.some(term => keywordLower.includes(term));
        });

        console.log(`🎯 Generated ${aiKeywords.length} location-filtered keywords`);

        // Ensure we have keywords
        if (Array.isArray(aiKeywords) && aiKeywords.length > 0) {
          return aiKeywords.slice(0, 4);
        }
      } catch (parseError) {
        console.warn('Failed to parse AI keywords JSON, using fallback');
      }

      // Fallback keywords if parsing fails
      return this.getFallbackKeywords(companyName).slice(0, 4);
    } catch (error) {
      console.error('AI keyword generation error:', error);
      return this.getFallbackKeywords(companyName).slice(0, 4);
    }
  }

  async generateAIKeywordsWithTrends(companyName, count = 4) {
    try {
      // First, get trend data for solar industry from multiple sources
      const trendService = require('./trendService');
      let trendData = [];

      try {
        console.log(`📊 Fetching comprehensive trend data for ${count} keyword generation...`);
        trendData = await trendService.fetchTrendData('solar energy', 'all', 10);
        console.log(`✅ Fetched ${trendData.length} trend articles for enhanced keyword context`);
      } catch (error) {
        console.warn('⚠️ Could not fetch trend data for keyword generation:', error.message);
      }

      // Create comprehensive trend context
      const trendContext = trendData.length > 0
        ? `Current industry trends and hot topics: ${trendData.map(t => t.title).slice(0, 5).join(', ')}`
        : 'Focus on evergreen solar industry topics with high search volume';

      const prompt = `Generate exactly ${count} high-value SEO keywords specifically for ${companyName} in the solar energy industry.
      Focus on ${companyName}'s core services and target audience.

      ${trendContext}

      COMPANY-SPECIFIC REQUIREMENTS FOR ${companyName}:
      - Focus on ${companyName}'s main services: Solar Design, Engineering, Permitting, Installation Support
      - Target solar professionals, installers, contractors, and project developers
      - Emphasize technical expertise and professional services
      - Include topics relevant to solar project development and implementation

      IMPORTANT GUIDELINES:
      - DO NOT include generic location keywords like "India", "USA", "California", "near me", etc.
      - DO NOT include generic terms like "best solar company" or "top solar installer"
      - Focus on technical, educational, and service-specific topics that ${companyName} can address
      - Include keywords related to solar design, engineering, permitting, and installation processes

      Focus on ${companyName}-relevant topics:
      1. Solar design and engineering processes
      2. Permitting and compliance requirements
      3. Installation best practices and technical guides
      4. Solar project development and management
      5. Industry standards and regulations
      6. Technical troubleshooting and optimization

      Return only a JSON array with exactly ${count} objects containing:
      - focusKeyword: the main keyword phrase (must be relevant to ${companyName}'s services)
      - articleFormat: one of "how-to", "guide", "listicle", "comparison", "review", "case-study", "analysis"
      - wordCount: suggested word count like "1500-2000" (MAX 2500 words)
      - targetAudience: specific target audience (focus on solar professionals)
      - objective: content goal (Lead Generation, Brand Awareness, Thought Leadership, etc.)
      - source: "vertex-trends"

      Example format for ${companyName}:
      [
        {"focusKeyword": "solar design software comparison 2024", "articleFormat": "comparison", "wordCount": "1800-2200", "targetAudience": "Solar Design Engineers", "objective": "Thought Leadership", "source": "vertex-trends"},
        {"focusKeyword": "solar permitting process optimization", "articleFormat": "guide", "wordCount": "1600-2000", "targetAudience": "Solar Installers", "objective": "Lead Generation", "source": "vertex-trends"}
      ]`;

      const response = await vertexService.generateContent(prompt, {
        name: companyName,
        tone: 'professional',
        targetAudience: 'Solar industry professionals and potential customers'
      });

      // Try to parse JSON from the response
      let aiKeywords = [];
      try {
        // Extract JSON from response if it's wrapped in text
        const jsonMatch = response.content.match(/\[[\s\S]*\]/);
        if (jsonMatch) {
          aiKeywords = JSON.parse(jsonMatch[0]);
        } else {
          aiKeywords = JSON.parse(response.content);
        }

        // Filter out any keywords that contain location terms
        const locationTerms = ['india', 'usa', 'california', 'texas', 'florida', 'new york', 'delhi', 'mumbai', 'bangalore', 'near me', 'in my area', 'local', 'best company', 'top company'];
        aiKeywords = aiKeywords.filter(keyword => {
          const keywordLower = keyword.focusKeyword.toLowerCase();
          return !locationTerms.some(term => keywordLower.includes(term));
        });

        // Ensure we have the requested number of keywords
        if (Array.isArray(aiKeywords) && aiKeywords.length > 0) {
          console.log(`✅ Generated ${aiKeywords.length} location-filtered trend-based keywords`);
          return aiKeywords.slice(0, count);
        }
      } catch (parseError) {
        console.warn('Failed to parse AI keywords with trends JSON, falling back to basic generation');
      }

      // Fallback to basic AI keyword generation
      return await this.generateAIKeywords(companyName);
    } catch (error) {
      console.error('AI keyword generation with trends error:', error);
      return await this.generateAIKeywords(companyName);
    }
  }

  getFallbackKeywords(companyName) {
    const companyLower = companyName ? companyName.toLowerCase() : '';

    // Ensite-specific fallback keywords
    if (companyLower.includes('ensite')) {
      return [
        {
          focusKeyword: "solar engineering design standards",
          articleFormat: "guide",
          wordCount: "1800-2200",
          targetAudience: "Solar engineers",
          objective: "technical education",
          source: "fallback"
        },
        {
          focusKeyword: "solar permit documentation requirements",
          articleFormat: "how-to",
          wordCount: "1600-2000",
          targetAudience: "Solar project managers",
          objective: "lead generation",
          source: "fallback"
        },
        {
          focusKeyword: "solar structural load analysis",
          articleFormat: "analysis",
          wordCount: "1500-1800",
          targetAudience: "Structural engineers",
          objective: "thought leadership",
          source: "fallback"
        },
        {
          focusKeyword: "solar project technical specifications",
          articleFormat: "guide",
          wordCount: "1700-2000",
          targetAudience: "Solar developers",
          objective: "education",
          source: "fallback"
        }
      ];
    }

    // Default solar industry related fallback keywords
    return [
      {
        focusKeyword: "solar panel installation guide",
        articleFormat: "guide",
        wordCount: "1500-2000",
        targetAudience: "Homeowners",
        objective: "Lead generation",
        source: "fallback"
      },
      {
        focusKeyword: "solar energy cost savings",
        articleFormat: "how-to",
        wordCount: "1200-1800",
        targetAudience: "Property owners",
        objective: "Education",
        source: "fallback"
      },
      {
        focusKeyword: "commercial solar system benefits",
        articleFormat: "comparison",
        wordCount: "1800-2200",
        targetAudience: "Business owners",
        objective: "Lead generation",
        source: "fallback"
      },
      {
        focusKeyword: "solar financing options",
        articleFormat: "guide",
        wordCount: "1400-1800",
        targetAudience: "Homeowners and businesses",
        objective: "Lead generation",
        source: "fallback"
      },
      {
        focusKeyword: "commercial solar design",
        articleFormat: "guide",
        wordCount: "1800-2000",
        targetAudience: "Business owners",
        objective: "Lead generation",
        source: "fallback"
      },
      {
        focusKeyword: "solar permit process",
        articleFormat: "how-to",
        wordCount: "1000-1500",
        targetAudience: "Solar installers",
        objective: "Education",
        source: "fallback"
      },
      {
        focusKeyword: "solar system maintenance",
        articleFormat: "guide",
        wordCount: "1200-1600",
        targetAudience: "Solar owners",
        objective: "Customer retention",
        source: "fallback"
      }
    ];
  }

  async getUsedKeywords() {
    try {
      console.log('🔍 Checking for already used keywords...');

      const usedKeywords = new Set();

      // 1. Get keywords from BlogData (original blog entries)
      const blogData = await BlogData.find({}, 'focusKeyword');
      blogData.forEach(blog => {
        if (blog.focusKeyword && blog.focusKeyword !== 'placeholder') {
          usedKeywords.add(blog.focusKeyword.toLowerCase().trim());
        }
      });

      // 2. Get keywords from Drafts (selected keywords)
      const drafts = await Draft.find({}, 'selectedKeyword').populate('blogId', 'focusKeyword');
      drafts.forEach(draft => {
        // Check selectedKeyword from draft
        if (draft.selectedKeyword && draft.selectedKeyword !== 'placeholder') {
          usedKeywords.add(draft.selectedKeyword.toLowerCase().trim());
        }
        // Check focusKeyword from associated blogId
        if (draft.blogId?.focusKeyword && draft.blogId.focusKeyword !== 'placeholder') {
          usedKeywords.add(draft.blogId.focusKeyword.toLowerCase().trim());
        }
      });

      const usedKeywordsArray = Array.from(usedKeywords);
      console.log(`📋 Found ${usedKeywordsArray.length} used keywords:`, usedKeywordsArray);

      return usedKeywordsArray;
    } catch (error) {
      console.error('❌ Error getting used keywords:', error);
      return [];
    }
  }

  /**
   * Generate company-specific keywords based on services
   * @param {string} companyName - Company name
   * @param {string} services - Company services
   * @returns {Array} Company-specific keywords
   */
  generateCompanySpecificKeywords(companyName, services) {
    console.log(`🏢 Generating company-specific keywords for ${companyName}`);

    const companyLower = companyName.toLowerCase();

    if (companyLower.includes('ensite')) {
      return [
        {
          focusKeyword: 'solar structural engineering requirements',
          articleFormat: 'guide',
          wordCount: '1800-2200',
          targetAudience: 'Solar engineers and developers',
          objective: 'technical education',
          source: 'company-specific'
        },
        {
          focusKeyword: 'solar permit design best practices',
          articleFormat: 'how-to',
          wordCount: '1600-2000',
          targetAudience: 'Solar project managers',
          objective: 'lead generation',
          source: 'company-specific'
        },
        {
          focusKeyword: 'solar project engineering standards',
          articleFormat: 'analysis',
          wordCount: '1500-1800',
          targetAudience: 'Solar engineering professionals',
          objective: 'thought leadership',
          source: 'company-specific'
        },
        {
          focusKeyword: 'solar technical consulting services',
          articleFormat: 'guide',
          wordCount: '1700-2000',
          targetAudience: 'Solar developers and contractors',
          objective: 'lead generation',
          source: 'company-specific'
        }
      ];
    } else if (companyLower.includes('wattmonk')) {
      return [
        {
          focusKeyword: 'solar design and engineering services',
          articleFormat: 'guide',
          wordCount: '2000-2500',
          targetAudience: 'Solar installers and contractors',
          objective: 'lead generation',
          source: 'company-specific'
        },
        {
          focusKeyword: 'solar PTO interconnection process',
          articleFormat: 'how-to',
          wordCount: '1800-2200',
          targetAudience: 'Solar developers',
          objective: 'education',
          source: 'company-specific'
        },
        {
          focusKeyword: 'solar permit approval services',
          articleFormat: 'guide',
          wordCount: '1500-2000',
          targetAudience: 'Solar contractors',
          objective: 'lead generation',
          source: 'company-specific'
        },
        {
          focusKeyword: 'solar stamping and certification',
          articleFormat: 'comparison',
          wordCount: '1200-1800',
          targetAudience: 'Solar professionals',
          objective: 'brand awareness',
          source: 'company-specific'
        }
      ];
    }

    // Default keywords for other companies
    return [
      {
        focusKeyword: 'solar energy solutions',
        articleFormat: 'guide',
        wordCount: '2000-2500',
        targetAudience: 'General audience',
        objective: 'brand awareness',
        source: 'company-specific'
      }
    ];
  }
}

module.exports = new KeywordService();
