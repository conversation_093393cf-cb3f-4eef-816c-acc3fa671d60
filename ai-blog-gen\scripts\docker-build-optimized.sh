#!/bin/bash

echo "🚀 Building optimized Docker images..."

# Clean up first
echo "🧹 Cleaning up old images..."
docker image prune -f

# Build with optimizations
echo "📦 Building backend (optimized)..."
docker build \
  --target production \
  --no-cache \
  -t blog_gen-backend:latest \
  ./ai-blog-platform-backend

echo "📦 Building frontend (optimized)..."
docker build \
  --target production \
  --no-cache \
  -t blog_gen-frontend:latest \
  ./ai-blog-platform-frontend

# Show image sizes
echo "📊 Image sizes:"
docker images | grep blog_gen

echo "✅ Optimized build complete!"
echo "Expected sizes:"
echo "  Backend: ~150MB (down from 460MB)"
echo "  Frontend: ~100MB"