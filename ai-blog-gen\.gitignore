# Dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*

# Production builds
dist/
build/
.next/
out/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE and editor files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Logs
logs
*.log

# Runtime data
pids
*.pid
*.seed
*.pid.lock

# Coverage directory used by tools like istanbul
coverage/
*.lcov

# nyc test coverage
.nyc_output

# Dependency directories
jspm_packages/

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Microbundle cache
.rpt2_cache/
.rts2_cache_cjs/
.rts2_cache_es/
.rts2_cache_umd/

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# parcel-bundler cache (https://parceljs.org/)
.cache
.parcel-cache

# Next.js build output
.next

# Nuxt.js build / generate output
.nuxt

# Gatsby files
.cache/
public

# Storybook build outputs
.out
.storybook-out

# Temporary folders
tmp/
temp/

# Uploads directory (contains user uploaded files)
uploads/

# Database files
*.db
*.sqlite
*.sqlite3

# Package lock files (keep only one)
package-lock.json
yarn.lock
pnpm-lock.yaml

# TypeScript cache
*.tsbuildinfo

# Optional stylelint cache
.stylelintcache

# SvelteKit build / generate output
.svelte-kit

# Prisma
prisma/migrations/

# Test results and reports
test-results/
screenshots/
*.png
*.jpg
*.jpeg

# Deployment and production files
ecosystem.config.js
docker-compose.override.yml

# Backup files
*.backup
*.bak
backups/
dumps/

# SSL certificates and keys
*.pem
*.key
*.crt
*.csr

# Cloud provider credentials
.aws/
gcloud/
*.json
